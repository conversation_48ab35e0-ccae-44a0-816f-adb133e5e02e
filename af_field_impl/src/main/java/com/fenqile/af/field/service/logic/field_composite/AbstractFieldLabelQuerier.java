package com.fenqile.af.field.service.logic.field_composite;

import com.fenqile.af.field.model.bean.fieldlabel.FieldLabelPagingQueryBaseParams;
import com.fenqile.af.field.model.bean.fieldlabel.FieldLabelPagingQueryBaseResult;
import com.fenqile.af.field.model.bean.fieldlabel.FieldLabelPagingQueryTrans;
import com.fenqile.af.field.vo.PageResVo;

/**
 * 查询字段和标签列表抽象逻辑类
 *
 * <AUTHOR> djhuang
 * @create 2023/3/31 15:59
 */
public abstract class AbstractFieldLabelQuerier {

    /**
     * 查询前准备工作
     *
     * @param queryParams 查询参数
     * @return
     */
    protected abstract Object prepareQuery(FieldLabelPagingQueryBaseParams queryParams);


    /**
     * 查询
     *
     * @param queryParams 查询参数
     * @param prepare     准备工作结果
     * @return
     */
    protected abstract FieldLabelPagingQueryTrans onQuery(FieldLabelPagingQueryBaseParams queryParams, Object prepare);


    /**
     * 查询后工作
     *
     * @param queryParams 查询参数
     * @param trans       查询中转结果
     */
    protected abstract PageResVo<FieldLabelPagingQueryBaseResult> afterQuery(FieldLabelPagingQueryBaseParams queryParams, FieldLabelPagingQueryTrans trans);

    /**
     * 分页查询
     *
     * @param queryParams 查询参数
     * @return
     */
    PageResVo<FieldLabelPagingQueryBaseResult> queryByPage(FieldLabelPagingQueryBaseParams queryParams) {

        Object prepareQuery = this.prepareQuery(queryParams);

        FieldLabelPagingQueryTrans query = this.onQuery(queryParams, prepareQuery);

        return this.afterQuery(queryParams, query);

    }


}
