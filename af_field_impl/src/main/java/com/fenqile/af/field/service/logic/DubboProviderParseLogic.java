package com.fenqile.af.field.service.logic;

import com.fenqile.af.field.bean.FieldDependServiceBean;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Node;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

/**
 * dubbo依赖服务解析
 *
 * <AUTHOR>
 * @since 2018年2月8日
 */
@Service("dubboProviderParseLogic")
public class DubboProviderParseLogic {

    private static final Logger LOGGER = LoggerFactory.getLogger(DubboProviderParseLogic.class);

    /**
     * 读取xml文档
     *
     * @param filePath
     * @return
     * @throws DocumentException
     * <AUTHOR>
     * @since 2018年2月9日
     */
    public Document parse(String filePath) throws DocumentException {
        SAXReader reader = new SAXReader();
        return reader.read(new File(filePath));
    }

    /**
     * 将xml依赖服务内容转换为list列表
     *
     * @param filePath
     * @param adapter
     * @return
     * <AUTHOR>
     * @since 2018年2月9日
     */
    @SuppressWarnings("unchecked")
    public List<FieldDependServiceBean> parse2List(String filePath, int adapter) {

        Document document = null;
        try {
            document = this.parse(filePath);
        } catch (DocumentException e) {
            LOGGER.error("parse xml error, filePath={}", filePath);
            return null;
        }

        if (document == null) {
            return null;
        }

        List<Node> nodeList = document.selectNodes("//beans/dubbo:reference");

        if (nodeList == null || nodeList.isEmpty()) {
            LOGGER.error("xml nodeList is null, filePath={}", filePath);
            return null;
        }

        List<FieldDependServiceBean> list = Lists.newArrayList();

        for (Node node : nodeList) {
            String serviceName = node.valueOf("@interface");
            String group = node.valueOf("@group");
            String serviceVersion = node.valueOf("@version");
            String timeoutStr = node.valueOf("@timeout");
            int timeout = 0;
            if (StringUtils.isNotBlank(timeoutStr)) {
                timeout = Integer.parseInt(timeoutStr);
            }

            FieldDependServiceBean bean = new FieldDependServiceBean();
            bean.setAdapter(adapter);
            bean.setServiceName(serviceName);
            bean.setGroup(group);
            bean.setServiceVersion(serviceVersion);
            bean.setTimeout(timeout);

            list.add(bean);
        }

        return list;
    }
}
