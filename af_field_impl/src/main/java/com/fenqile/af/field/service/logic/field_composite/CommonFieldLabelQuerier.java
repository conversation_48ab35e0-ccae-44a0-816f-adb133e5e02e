package com.fenqile.af.field.service.logic.field_composite;

import cn.hutool.core.collection.CollectionUtil;
import com.fenqile.af.field.bean.FieldInfoBean;
import com.fenqile.af.field.bean.FieldQueryBean;
import com.fenqile.af.field.constants.HippoConfigConstants;
import com.fenqile.af.field.constants.SuojiConstants;
import com.fenqile.af.field.model.bean.fieldlabel.FieldLabelPagingQueryBaseParams;
import com.fenqile.af.field.model.bean.fieldlabel.FieldLabelPagingQueryBaseResult;
import com.fenqile.af.field.model.bean.fieldlabel.FieldLabelPagingQueryTrans;
import com.fenqile.af.field.proxy.FeaturePortalServiceProxy;
import com.fenqile.af.field.service.logic.FieldInfoQueryServiceLogic;
import com.fenqile.af.field.service.logic.label.LabelManagementLogic;
import com.fenqile.af.field.vo.PageResVo;
import com.fenqile.label.management.service.request.label.LabelListReq;
import com.fenqile.label.management.service.response.label.DTO.LabelMetaDTO;
import com.fenqile.label.management.service.response.label.DTO.LabelMetaListDTO;
import com.fenqile.risk.feature.platform.portal.request.feature.HawkQueryFeatureReq;
import com.fenqile.risk.feature.platform.portal.response.PageInfoVO;
import com.fenqile.risk.feature.platform.portal.response.feature.HawkQueryFeatureResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.fenqile.label.management.common.constants.CommonConstants.Digit.ZERO;

/**
 * 通用查询字段和标签列表查询器
 *
 * <AUTHOR> djhuang
 * @create 2023/3/31 19:16
 */
@Slf4j
@Component
public class CommonFieldLabelQuerier extends AbstractFieldLabelQuerier {

    @Autowired
    private FieldInfoQueryServiceLogic fieldInfoQueryServiceLogic;

    @Autowired
    private LabelManagementLogic labelManagementLogic;

    @Autowired
    private FeaturePortalServiceProxy featurePortalServiceProxy;


    @Override
    protected Object prepareQuery(FieldLabelPagingQueryBaseParams queryParams) {
        return null;
    }

    /**
     * 查询
     *
     * @param queryParams 查询参数
     * @param prepare     准备工作结果
     * @return
     */
    @Override
    protected FieldLabelPagingQueryTrans onQuery(FieldLabelPagingQueryBaseParams queryParams, Object prepare) {

        //1、 先查索骥字段列表
        FieldQueryBean fieldQueryBean = copy2FieldQueryBean(queryParams);
        List<FieldInfoBean> fieldInfoBeanList = queryParams.isNotQueryField() ? Collections.emptyList() : fieldInfoQueryServiceLogic.queryField(fieldQueryBean);
        int fieldTotal = queryParams.isNotQueryField() ? SuojiConstants.Digit.ZERO : fieldInfoQueryServiceLogic.count(fieldQueryBean);


        //2、如果索骥字段数量不足一页：查询特征列表和总数；
        //如果数据足够一页：查询特征总数与字段总数合并起来用于分页
        PageInfoVO<HawkQueryFeatureResp> featurePageRes;
        if (!queryParams.isNotQueryFeature()) {
            featurePageRes = queryFeatureIfNeed(queryParams, fieldTotal);
        } else {
            featurePageRes = new PageInfoVO<>();
            featurePageRes.setResultRows(Collections.emptyList());
            featurePageRes.setTotal(0L);
        }

        //3、如果索骥字段+特征数量不足一页：查询标签列表和总数；
        //如果数据足够一页：查询标签总数与（字段+特征）总数合并起来用于分页
        LabelMetaListDTO labelPageRes;
        if (!queryParams.isNotQueryLabel()) {
            int fieldFeatureTotal = fieldTotal + featurePageRes.getTotal().intValue();
            labelPageRes = queryLabelIfNeed(queryParams, fieldFeatureTotal);
        } else {
            labelPageRes = new LabelMetaListDTO();
            labelPageRes.setLabelMetaList(Collections.emptyList());
            labelPageRes.setTotal(0);
        }

        FieldLabelPagingQueryTrans trans = new FieldLabelPagingQueryTrans();
        trans.setFieldTotal(fieldTotal);
        trans.setFieldInfoBeanList(fieldInfoBeanList);
        trans.setLabelTotal(labelPageRes.getTotal());
        trans.setLabelMetaDTOList(labelPageRes.getLabelMetaList());
        trans.setFeatureTotal(featurePageRes.getTotal().intValue());
        trans.setFeatureInfoList(featurePageRes.getResultRows());
        return trans;
    }

    private LabelMetaListDTO queryLabelIfNeed(FieldLabelPagingQueryBaseParams queryParams, int fieldTotal) {

        LabelMetaListDTO labelPageRes;
        int remainder = queryParams.getOffset() + queryParams.getLimit() - fieldTotal;
        if (remainder > SuojiConstants.Digit.ZERO) {

            //1、索骥字段在这一页，查出部分数据，不足一页，需要补充离线标签
            // 两边接口分页参数不一样，标签用的是页码，所以转换一下
            int labelPageNo = remainder / queryParams.getLimit();
            int mod = remainder % queryParams.getLimit();

            if (labelPageNo > ZERO) {
                queryParams.setPageNo(labelPageNo);
                labelPageRes = this.queryLabelMeta(queryParams);

                labelPageRes.setLabelMetaList(labelPageRes.getLabelMetaList().stream().skip(mod).collect(Collectors.toList()));
            } else {
                labelPageRes = new LabelMetaListDTO();
                labelPageRes.setLabelMetaList(Lists.newArrayList());
                labelPageRes.setTotal(ZERO);
            }

            if (mod != ZERO) {
                queryParams.setPageNo(++labelPageNo);
                LabelMetaListDTO nextPageRes = this.queryLabelMeta(queryParams);
                labelPageRes.getLabelMetaList().addAll(nextPageRes.getLabelMetaList().stream().limit(mod).collect(Collectors.toList()));
                labelPageRes.setTotal(nextPageRes.getTotal());
            }

        } else {
            // 备份
            Integer limit = queryParams.getLimit();
            Integer pageNo = queryParams.getPageNo();
            Integer offset = queryParams.getOffset();
            // 2、当前页全是索骥字段，不用补充离线标签，只需要查询离线标签总数
            queryParams.setLimit(SuojiConstants.Digit.ONE);
            queryParams.setOffset(SuojiConstants.Digit.ZERO);
            queryParams.setPageNo(null);
            labelPageRes = this.queryLabelMeta(queryParams);

            // 不用补充离线标签, 这里把标签列表置空
            labelPageRes.setLabelMetaList(Collections.emptyList());
            // 还原
            queryParams.setLimit(limit);
            queryParams.setPageNo(pageNo);
            queryParams.setOffset(offset);
        }
        return labelPageRes;
    }

    private PageInfoVO<HawkQueryFeatureResp> queryFeatureIfNeed(FieldLabelPagingQueryBaseParams queryParams, int fieldTotal) {

        PageInfoVO<HawkQueryFeatureResp> featurePageRes;
        // 备份
        Integer limit = queryParams.getLimit();
        Integer pageNo = queryParams.getPageNo();
        Integer offset = queryParams.getOffset();

        int remainder = queryParams.getOffset() + queryParams.getLimit() - fieldTotal;
        if (remainder > SuojiConstants.Digit.ZERO && remainder < queryParams.getLimit()) {

            //1、索骥+特征：索骥字段在这一页，查出部分数据，不足一页，需要补充特征
            queryParams.setOffset(SuojiConstants.Digit.ZERO);
            queryParams.setLimit(remainder);
            queryParams.setPageNo(null);

            featurePageRes = this.queryFeatureMeta(queryParams);

        } else if (remainder >= queryParams.getLimit()) {

            //2、全是特征：当前页全部查特征
            queryParams.setOffset(queryParams.getOffset() - fieldTotal);
            queryParams.setLimit(queryParams.getLimit());
            queryParams.setPageNo(null);
            featurePageRes = this.queryFeatureMeta(queryParams);

        } else {

            //3、全是字段：当前页全是索骥字段，不用补充特征，只需要查询特征总数
            queryParams.setLimit(SuojiConstants.Digit.ONE);
            queryParams.setOffset(SuojiConstants.Digit.ZERO);
            queryParams.setPageNo(null);
            featurePageRes = this.queryFeatureMeta(queryParams);


            // 不用补充特征, 这里把特征列表置空
            featurePageRes.setResultRows(Collections.emptyList());
        }

        // 还原
        queryParams.setLimit(limit);
        queryParams.setPageNo(pageNo);
        queryParams.setOffset(offset);
        return featurePageRes;
    }

    /**
     * 合并索骥字段、特征、标签数据
     *
     * @param queryParams 查询参数
     * @param trans       查询中转结果
     */
    @Override
    protected PageResVo<FieldLabelPagingQueryBaseResult> afterQuery(FieldLabelPagingQueryBaseParams queryParams, FieldLabelPagingQueryTrans trans) {

        // 合并索骥字段、特征、标签的总数和列表数据
        PageResVo<FieldLabelPagingQueryBaseResult> pageResVo = new PageResVo<>();
        pageResVo.setTotalNum((long) trans.getTotal());
        pageResVo.setTotalPage((int) (pageResVo.getTotalNum() % queryParams.getLimit() == 0 ?
                pageResVo.getTotalNum() / queryParams.getLimit() :
                pageResVo.getTotalNum() / queryParams.getLimit() + 1));

        List<FieldLabelPagingQueryBaseResult> compositeQueryResList = trans.getFieldInfoBeanList().stream().map(this::convertField).collect(Collectors.toList());
        compositeQueryResList.addAll(trans.getFeatureInfoList().stream().map(this::convertFeature).collect(Collectors.toList()));
        compositeQueryResList.addAll(trans.getLabelMetaDTOList().stream().map(this::convertLabel).collect(Collectors.toList()));
        pageResVo.setResultRows(compositeQueryResList);
        return pageResVo;

    }

    /**
     * 查询离线标签
     *
     * @param queryParams
     * @return
     */
    private LabelMetaListDTO queryLabelMeta(FieldLabelPagingQueryBaseParams queryParams) {

        LabelMetaListDTO result = new LabelMetaListDTO();
        result.setTotal(SuojiConstants.Digit.ZERO);
        result.setLabelMetaList(Lists.newArrayListWithCapacity(SuojiConstants.Digit.ZERO));

        // 如果不查标签，直接返回
        if (queryParams.getOffset() < SuojiConstants.Digit.ZERO) {
            return result;
        }

        // 转换查询离线标签参数
        LabelListReq queryBean = copy2LabelListReq(queryParams);

        log.debug("labelManagementLogic.getList req: {}", queryBean);
        result = labelManagementLogic.getList(queryBean);
        log.debug("labelManagementLogic.getList, resp: {}", result);

        return result;
    }

    /**
     * 查询特征
     *
     * @param queryParams
     * @return
     */
    private PageInfoVO<HawkQueryFeatureResp> queryFeatureMeta(FieldLabelPagingQueryBaseParams queryParams) {

        PageInfoVO<HawkQueryFeatureResp> result = new PageInfoVO<>();
        // 如果不查特征，直接返回
        if (queryParams.getOffset() < SuojiConstants.Digit.ZERO) {
            return result;
        }

        // 转换查询特征参数
        HawkQueryFeatureReq queryBean = copy2FeatureListReq(queryParams);
        result = featurePortalServiceProxy.queryListByPage(queryBean);

        // 如果总数为空，设置为0
        result.setTotal(result.getTotal() == null ? 0L : result.getTotal());

        return result;
    }

    protected FieldQueryBean copy2FieldQueryBean(FieldLabelPagingQueryBaseParams queryParams) {
        if (queryParams == null) {
            return null;
        }

        FieldQueryBean queryBean = new FieldQueryBean();
        queryBean.setLimit(queryParams.getLimit());
        queryBean.setOffset(queryParams.getOffset());

        //迁移且验证通过的字段，索骥字段列表不展示这些字段，需要过滤
        List<String> migratedVerifiedFieldNames = HippoConfigConstants.migratedVerifiedFieldNames();
        if (CollectionUtils.isNotEmpty(migratedVerifiedFieldNames)) {
            queryBean.setExcludeFieldNameList(migratedVerifiedFieldNames);
        }

        return queryBean;
    }

    protected LabelListReq copy2LabelListReq(FieldLabelPagingQueryBaseParams queryParams) {
        if (queryParams == null) {
            return null;
        }

        LabelListReq queryBean = new LabelListReq();
        queryBean.setPageSize(queryParams.getLimit());
        queryBean.setPageNo(queryParams.getPageNo() == null ? (queryParams.getOffset() / queryParams.getLimit()) + SuojiConstants.Digit.ONE : queryParams.getPageNo());
        queryBean.setIsNeedEnum(false);
        queryBean.setIsNeedVersionInfo(true);

        // 使用了computationTimelinessList这个参数，那么就必须把isNeedSuoJiField设置为false，否则会报错
        queryBean.setIsNeedSuoJiField(false);
        queryBean.setComputationTimelinessList(SuojiConstants.QUERY_LABEL_COMPUTATION_TIMELINESS_LIST);

        // 字段名和标签重名的字段、迁移中未验证通过的字段，离线标签不展示这些字段，需要过滤
        List<String> excludeLabelNameList = org.apache.commons.compress.utils.Lists.newArrayList();
        CollectionUtil.addAll(excludeLabelNameList, HippoConfigConstants.suoJiDuplicationNameFields());
        CollectionUtil.addAll(excludeLabelNameList, HippoConfigConstants.migratingUnVerifiedFieldNames());
        if (CollectionUtils.isNotEmpty(excludeLabelNameList)) {
            queryBean.setExcludeLabelNameList(excludeLabelNameList);
        }
        return queryBean;
    }

    protected HawkQueryFeatureReq copy2FeatureListReq(FieldLabelPagingQueryBaseParams queryParams) {
        if (queryParams == null) {
            return null;
        }

        HawkQueryFeatureReq queryBean = new HawkQueryFeatureReq();
        queryBean.setLimit(queryParams.getLimit());
        queryBean.setOffset(queryParams.getOffset());
        return queryBean;
    }

    protected FieldLabelPagingQueryBaseResult convertField(FieldInfoBean fieldInfoBean) {
        if (fieldInfoBean == null) {
            return null;
        }

        FieldLabelPagingQueryBaseResult result = new FieldLabelPagingQueryBaseResult();
        BeanUtils.copyProperties(fieldInfoBean, result);
        return result;
    }

    protected FieldLabelPagingQueryBaseResult convertLabel(LabelMetaDTO labelMetaDTO) {
        if (labelMetaDTO == null) {
            return null;
        }

        return new FieldLabelPagingQueryBaseResult();
    }

    protected FieldLabelPagingQueryBaseResult convertFeature(HawkQueryFeatureResp queryFeatureResp) {
        if (queryFeatureResp == null) {
            return null;
        }

        return new FieldLabelPagingQueryBaseResult();
    }

}
