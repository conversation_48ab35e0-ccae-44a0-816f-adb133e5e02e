package com.fenqile.af.field.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 用于http restful 接口调用
 */
public class HttpRequestUtils {

    private final static Logger LOGGER = LoggerFactory.getLogger(HttpRequestUtils.class);

    /**
     * 创建HttpClient用
     */
    private static HttpClientBuilder HTTP_CLIENT_BUILDER;

    /**
     * client对象
     */
    private static HttpClient HTTP_CLIENT;


    static {
        HTTP_CLIENT_BUILDER = HttpClientBuilder.create();
        HTTP_CLIENT_BUILDER.setConnectionTimeToLive(10, TimeUnit.SECONDS);
        RequestConfig.Builder requestBuilder = RequestConfig.custom();
        requestBuilder.setConnectTimeout(10000).setConnectionRequestTimeout(
                10000).setSocketTimeout(10000);
        HTTP_CLIENT_BUILDER.setDefaultRequestConfig(requestBuilder.build());
        HTTP_CLIENT = HTTP_CLIENT_BUILDER.build();
    }

    /**
     * 发送GET请求，参数为Key-Value形式
     *
     * @param headers 请求头
     * @param url     地址
     * @param params  参数
     * @return
     */
    public static String doGet(Map<String, String> headers, String url, Map<String, Object> params) {
        String apiUrl = url;
        StringBuffer param = new StringBuffer();
        int i = 0;
        for (String key : params.keySet()) {
            if (i == 0) {
                param.append("?");
            } else {
                param.append("&");
            }
            param.append(key).append("=").append(params.get(key));
            i++;
        }
        apiUrl += param;
        String result = null;

        try {
            HttpGet httpGet = new HttpGet(apiUrl);
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpGet.setHeader(entry.getKey(), entry.getValue());
            }
            HttpResponse response = HTTP_CLIENT.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            LOGGER.info("get请求状态码：{}", statusCode);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                InputStream instream = entity.getContent();
                result = IOUtils.toString(instream, StandardCharsets.UTF_8);
            }
        } catch (Exception e) {
            LOGGER.error("get请求异常:{},api:{}", params, apiUrl, e);
        }
        return result;
    }

    /**
     * 发送POST请求，参数JSON格式
     *
     * @param headers 请求头
     * @param apiUrl  地址
     * @param json    参数
     * @return
     */
    public static int doPost(Map<String, String> headers, String apiUrl, String json) {
        HttpPost httpPost = new HttpPost(apiUrl);
        CloseableHttpResponse response = null;
        try {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
            StringEntity stringEntity = new StringEntity(json, "UTF-8");
            stringEntity.setContentType("application/json");
            httpPost.setEntity(stringEntity);
            LOGGER.info("post更新请求title:{}", JSONObject.toJSONString(httpPost));
            response = (CloseableHttpResponse) HTTP_CLIENT.execute(httpPost);
            LOGGER.info("post更新结果：{}", JSONObject.toJSONString(response));
            return response.getStatusLine().getStatusCode();
        } catch (Exception e) {
            LOGGER.error("post请求异常:{},api:{}", json, apiUrl, e);
        } finally {
            if (response != null) {
                try {
                    EntityUtils.consume(response.getEntity());
                } catch (IOException e) {
                    LOGGER.error("consume异常:{},api:{}", json, apiUrl, e);
                }
            }
        }
        return -1;//自定义的错误码，区别与http的错误码
    }

    /**
     * 发送PUT请求，参数JSON格式
     *
     * @param headers 请求头
     * @param apiUrl  地址
     * @param json    参数
     * @return
     */
    public static int doPut(Map<String, String> headers, String apiUrl, String json) {
        HttpPut httpPut = new HttpPut(apiUrl);
        CloseableHttpResponse response = null;
        try {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPut.setHeader(entry.getKey(), entry.getValue());
            }
            StringEntity stringEntity = new StringEntity(json, "UTF-8");// 解决中文乱码问题
            stringEntity.setContentType("application/json");
            httpPut.setEntity(stringEntity);
            LOGGER.info("Put更新请求title:{}", JSONObject.toJSONString(httpPut));
            response = (CloseableHttpResponse) HTTP_CLIENT.execute(httpPut);
            LOGGER.info("Put更新结果：{}", JSONObject.toJSONString(response));
            return response.getStatusLine().getStatusCode();
        } catch (Exception e) {
            LOGGER.error("put请求异常:{},api:{}", json, apiUrl, e);
        } finally {
            if (response != null) {
                try {
                    EntityUtils.consume(response.getEntity());
                } catch (IOException e) {
                    LOGGER.error("consume异常:{},api:{}", json, apiUrl, e);
                }
            }
        }
        return -1;//自定义的错误码，区别与http的错误码
    }


    /**
     * 通过POST方法向指定URL发送JSON数据，并返回服务器响应的字符串内容。
     *
     * @param url  请求的URL地址。
     * @param json 要发送的JSON数据。
     * @return 服务器响应的字符串内容，如果请求失败或输入参数无效，则返回null。
     */
    public static String postJsonData(String url, String json) {

        LOGGER.info("post请求url:{},json:{}", url, json);

        // 检查输入参数是否有效，如果无效则记录错误并返回null。
        if (StringUtils.isBlank(json) || StringUtils.isBlank(url)) {
            LOGGER.error("Invalid input, json:{}, url:{}", json, url);
            return null;
        }

        // 创建HttpPost对象并设置请求URL。
        HttpPost httpPost = new HttpPost(url);
        // 设置请求头，指定请求体的Content-Type为application/json。
        httpPost.addHeader(HTTP.CONTENT_TYPE, "application/json");

        // 创建StringEntity对象，包含待发送的JSON数据，并设置字符编码为UTF-8。
        StringEntity se = new StringEntity(json, StandardCharsets.UTF_8);
        httpPost.setEntity(se);

        // 为了try-with-resources语句确保资源的正确关闭，创建一个wrapper
        try (CloseableHttpResponse resp = (CloseableHttpResponse) HTTP_CLIENT.execute(httpPost)) {
            HttpEntity entity = resp.getEntity();
            // 如果响应实体不为空。
            if (entity != null) {
                // 检查HTTP响应状态码
                int statusCode = resp.getStatusLine().getStatusCode();
                // 如果状态码为200，表示请求成功，返回响应内容。
                if (statusCode == HttpStatus.SC_OK) {
                    return EntityUtils.toString(entity, StandardCharsets.UTF_8);
                } else {
                    // 如果状态码不为200，记录错误信息并返回null。
                    LOGGER.warn("Invalid HTTP status code: {},json:{},url:{}", statusCode, json, url);
                    // 确保释放资源
                    EntityUtils.consume(entity);
                }
            } else {
                // 如果响应实体为空，记录错误信息并返回null。
                LOGGER.error("Response entity is null,json:{},url:{}", json, url);
            }
        } catch (Throwable e) {
            // 如果发生异常，记录错误信息并重新抛出异常。
            LOGGER.warn("Error during HTTP request, json:{}, url:{}", json, url, e);
        }
        return null;
    }

}
